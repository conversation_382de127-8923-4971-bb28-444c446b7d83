<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ExternalDealers extends Model
{
    protected $connection = 'mysql'; // farklı veritabanı
    protected $table = 'branches';     // mysql'deki tablo adı
    protected $primaryKey = 'id';     // opsiyonel
    public $timestamps = false;               // timestamps yoksa

    protected $fillable = [  
        'kod',
        "belge_kod",
        'category_id',
        'kisa_ad',
        'unvan',
        'ad',
        'soyad',
        'mahalle',
        'cadde',
        'sokak',
        'semt',
        'il_kodu',
        'ilce_kodu',
        'konum',
        'telefon',
        'gsm',
        'email',
        'web',
        'vergi_dairesi',
        'vergi_no',
        'tc_no',
        'sozlesme',
        'sozlesme_baslangic_tarihi',
        'sozlesme_bitis_tarihi',
        'sozlesme_yil',
        'plus_kart_satis',
        'hasar_sorgulama',
        'plus_kart_yukle',
        'son_fiyat_degisiklik_tarihi',
        'yetkili_ad_soyad',
        'yetkili_gsm',
        'yetkili_dahili',
        'status',
    ];
}
