<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Models\Dealer;
use App\Models\Region;
use App\Services\DealerMigrationService;
use Exception;

class MigrateDealersFromMySQL extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'migrate:dealers-from-mysql
                            {--dry-run : Sad<PERSON>e önizleme yap, veri kaydetme}
                            {--force : Mevcut verileri sil ve yeniden yükle}
                            {--chunk=100 : Kaç kayıt gruplarında işle}
                            {--backup : Migration öncesi yedek al}
                            {--validate : Migration sonrası doğrulama yap}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'MySQL branches tablosundan dealers verilerini PostgreSQL\'e taşır';

    private DealerMigrationService $migrationService;

    public function __construct(DealerMigrationService $migrationService)
    {
        parent::__construct();
        $this->migrationService = $migrationService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 MySQL Branches\'den PostgreSQL Dealers\'e Migration Başlatılıyor...');
        
        $dryRun = $this->option('dry-run');
        $force = $this->option('force');
        $chunkSize = (int) $this->option('chunk');
        $backup = $this->option('backup');
        $validate = $this->option('validate');

        try {
            // Migration istatistiklerini göster
            $this->showMigrationStats();

            // Region kontrolü
            if (!$this->checkRegions()) {
                return 1;
            }

            // Yedek al
            if ($backup && !$dryRun) {
                $this->info('💾 Mevcut dealers verileri yedekleniyor...');
                $backupFile = $this->migrationService->backupCurrentDealers();
                $this->info("✅ Yedek oluşturuldu: {$backupFile}");
            }

            // MySQL ve PostgreSQL bağlantılarını test et
            $this->info('📡 Bağlantılar test ediliyor...');
            $this->testConnections();

            // Mevcut verileri kontrol et
            $existingCount = Dealer::count();
            if ($existingCount > 0 && !$force) {
                $this->warn("⚠️  PostgreSQL'de {$existingCount} adet dealer kaydı bulundu.");
                if (!$this->confirm('Devam etmek istiyor musunuz? (Mevcut veriler korunacak)')) {
                    $this->info('❌ İşlem iptal edildi.');
                    return 0;
                }
            }

            // Force seçeneği ile mevcut verileri sil
            if ($force && $existingCount > 0) {
                $this->warn("🗑️  Force seçeneği aktif. {$existingCount} adet mevcut kayıt silinecek...");
                if (!$dryRun) {
                    Dealer::truncate();
                    $this->info('✅ Mevcut veriler silindi.');
                }
            }

            // MySQL'den verileri çek
            $this->info('📥 MySQL\'den branches verileri çekiliyor...');
            $mysqlBranches = $this->migrationService->fetchMySQLBranches();
            
            if (empty($mysqlBranches)) {
                $this->warn('⚠️  MySQL\'de branches verisi bulunamadı.');
                return 0;
            }

            $this->info("📊 MySQL'de {" . count($mysqlBranches) . "} adet branch bulundu.");

            // Verileri işle
            $this->processBranches($mysqlBranches, $dryRun, $chunkSize);

            // Doğrulama yap
            if ($validate && !$dryRun) {
                $this->info('🔍 Migration doğrulanıyor...');
                $issues = $this->migrationService->validateMigration();
                
                if (empty($issues)) {
                    $this->info('✅ Doğrulama başarılı. Tüm veriler doğru şekilde taşındı.');
                } else {
                    $this->warn('⚠️  Doğrulama sorunları:');
                    foreach ($issues as $issue) {
                        $this->warn("  - {$issue}");
                    }
                }
            }

            $this->info('🎉 Migration başarıyla tamamlandı!');
            
        } catch (Exception $e) {
            $this->error('❌ Hata: ' . $e->getMessage());
            $this->error('📍 Dosya: ' . $e->getFile() . ':' . $e->getLine());
            return 1;
        }

        return 0;
    }

    /**
     * Migration istatistiklerini göster
     */
    private function showMigrationStats()
    {
        $stats = $this->migrationService->getMigrationStats();
        
        $this->info('📊 Migration İstatistikleri:');
        $this->table(['Kaynak', 'Değer'], [
            ['MySQL Branches', $stats['mysql_branches']],
            ['PostgreSQL Dealers', $stats['postgresql_dealers']],
            ['Mevcut Regions', $stats['available_regions']],
            ['Mevcut Yedekler', $stats['available_backups']],
            ['Son Yedek', $stats['last_backup']['timestamp'] ?? 'Yok'],
        ]);
    }

    /**
     * Region kontrolü
     */
    private function checkRegions(): bool
    {
        $regionCount = Region::count();
        
        if ($regionCount === 0) {
            $this->error('❌ PostgreSQL\'de hiç region bulunamadı!');
            $this->error('💡 Önce regions migration\'ını çalıştırın: php artisan migrate:regions-from-mysql');
            return false;
        }

        $this->info("✅ {$regionCount} adet region bulundu.");
        return true;
    }

    /**
     * Bağlantıları test et
     */
    private function testConnections()
    {
        try {
            DB::connection('mysql')->getPdo();
            $this->info('✅ MySQL bağlantısı başarılı.');
        } catch (Exception $e) {
            throw new Exception('MySQL bağlantısı başarısız: ' . $e->getMessage());
        }

        try {
            DB::connection()->getPdo();
            $this->info('✅ PostgreSQL bağlantısı başarılı.');
        } catch (Exception $e) {
            throw new Exception('PostgreSQL bağlantısı başarısız: ' . $e->getMessage());
        }
    }

    /**
     * Branches verilerini işle ve PostgreSQL'e dealers olarak kaydet
     */
    private function processBranches($mysqlBranches, $dryRun, $chunkSize)
    {
        $chunks = array_chunk($mysqlBranches, $chunkSize);
        $totalProcessed = 0;
        $totalErrors = 0;

        $progressBar = $this->output->createProgressBar(count($mysqlBranches));
        $progressBar->start();

        foreach ($chunks as $chunk) {
            foreach ($chunk as $mysqlBranch) {
                try {
                    $dealerData = $this->mapBranchToDealer($mysqlBranch);

                    if ($dryRun) {
                        // Zone sayısını hesapla
                        $zoneCount = DB::connection('mysql')
                            ->table('zone_branches')
                            ->where('branch_id', $mysqlBranch->id)
                            ->whereNull('deleted_at')
                            ->count();

                        $this->line("\n🔍 Dry Run - İşlenecek veri:");
                        $this->table(['Alan', 'Değer'], [
                            ['ID (MySQL → PostgreSQL)', $dealerData['id'] ?? 'N/A'],
                            ['Name', $dealerData['name']],
                            ['Contact Person', $dealerData['contact_person'] ?? 'N/A'],
                            ['Phone', $dealerData['phone'] ?? 'N/A'],
                            ['Email', $dealerData['email'] ?? 'N/A'],
                            ['City', $dealerData['city'] ?? 'N/A'],
                            ['Status', $dealerData['status'] ? 'Aktif' : 'Pasif'],
                            ['Zone Count', $zoneCount . ' zone ile ilişkili'],
                        ]);
                    } else {
                        // Aynı isimde dealer var mı kontrol et
                        $existingDealer = Dealer::where('name', $dealerData['name'])->first();

                        if ($existingDealer) {
                            $this->warn("\n⚠️  '{$dealerData['name']}' isimli dealer zaten mevcut. Atlanıyor...");
                        } else {
                            // Dealer'ı oluştur
                            $dealer = Dealer::create($dealerData);

                            // Dealer-Region ilişkilerini kaydet
                            $regionCount = $this->migrationService->saveDealerRegionRelationships($dealer->id, $mysqlBranch->id);

                            $totalProcessed++;

                            if ($regionCount > 0) {
                                $this->info("✅ '{$dealerData['name']}' dealer'ı {$regionCount} region ile ilişkilendirildi.");
                            }
                        }
                    }
                    
                    $progressBar->advance();
                    
                } catch (Exception $e) {
                    $totalErrors++;
                    $this->error("\n❌ Hata (ID: {$mysqlBranch->id}): " . $e->getMessage());
                    $progressBar->advance();
                }
            }
        }

        $progressBar->finish();
        $this->newLine(2);

        if ($dryRun) {
            $this->info("🔍 Dry Run tamamlandı. {" . count($mysqlBranches) . "} kayıt işlenebilir durumda.");
        } else {
            $this->info("✅ {$totalProcessed} kayıt başarıyla işlendi.");
            if ($totalErrors > 0) {
                $this->warn("⚠️  {$totalErrors} kayıtta hata oluştu.");
            }
        }
    }

    /**
     * MySQL branch verisini PostgreSQL dealer formatına dönüştür
     */
    private function mapBranchToDealer($mysqlBranch)
    {
        return [
            'id' => $mysqlBranch->id,
            'name' => $mysqlBranch->kisa_ad ?? 'Bilinmeyen Bayi',
            'contact_person' => $mysqlBranch->yetkili_ad_soyad ? $mysqlBranch->yetkili_ad_soyad : 'Bilinmeyen Kişi',
            'phone' => $mysqlBranch->yetkili_gsm ? $mysqlBranch->yetkili_gsm : null,
            'email' => $mysqlBranch->email ?? null,
            'address' => ($mysqlBranch->mahalle && $mysqlBranch->cadde && $mysqlBranch->sokak)
                ? $mysqlBranch->mahalle . ', ' . $mysqlBranch->cadde . ', ' . $mysqlBranch->sokak
                : null,
            'city' => $mysqlBranch->il_kodu ?? null,
            'district' => $mysqlBranch->semt ?? null,
            'status' => isset($mysqlBranch->status) ? (bool) $mysqlBranch->status : false,
            'created_at' => $mysqlBranch->created_at ?? now(),
            'updated_at' => $mysqlBranch->updated_at ?? now(),
        ];
    }
}
