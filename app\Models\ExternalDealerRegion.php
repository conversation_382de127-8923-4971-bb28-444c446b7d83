<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ExternalDealerRegion extends Model
{
    protected $connection = 'mysql'; // farklı veritabanı
    protected $table = 'zone_branches';     // mysql'deki tablo adı
    protected $primaryKey = 'id';     // opsiyonel
    protected $foreignKey = 'dealer_id';     // opsiyonel
    public $timestamps = false;               // timestamps yoksa

    protected $fillable = [
        'zone_id',
        'branch_id',
    ];
}
