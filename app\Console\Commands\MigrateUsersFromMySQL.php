<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Models\User;
use App\Models\Role;
use App\Services\UserMigrationService;
use Exception;

class MigrateUsersFromMySQL extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'migrate:users-from-mysql 
                            {--dry-run : <PERSON><PERSON>e önizleme yap, veri kaydetme}
                            {--force : Mevcut verileri sil ve yeniden yükle}
                            {--chunk=100 : Kaç kayıt gruplarında işle}
                            {--backup : Migration öncesi yedek al}
                            {--validate : Migration sonrası doğrulama yap}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'MySQL users tablosundan users verilerini PostgreSQL\'e taşır';

    private UserMigrationService $migrationService;

    public function __construct(UserMigrationService $migrationService)
    {
        parent::__construct();
        $this->migrationService = $migrationService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 MySQL Users\'den PostgreSQL Users\'e Migration Başlatılıyor...');
        
        $dryRun = $this->option('dry-run');
        $force = $this->option('force');
        $chunkSize = (int) $this->option('chunk');
        $backup = $this->option('backup');
        $validate = $this->option('validate');

        try {
            // Migration istatistiklerini göster
            $this->showMigrationStats();

            // Role kontrolü
            if (!$this->checkRoles()) {
                return 1;
            }

            // Yedek al
            if ($backup && !$dryRun) {
                $this->info('💾 Mevcut users verileri yedekleniyor...');
                $backupFile = $this->migrationService->backupCurrentUsers();
                $this->info("✅ Yedek oluşturuldu: {$backupFile}");
            }

            // MySQL ve PostgreSQL bağlantılarını test et
            $this->info('📡 Bağlantılar test ediliyor...');
            $this->testConnections();

            // Mevcut verileri kontrol et
            $existingCount = User::count();
            if ($existingCount > 0 && !$force) {
                $this->warn("⚠️  PostgreSQL'de {$existingCount} adet user kaydı bulundu.");
                if (!$this->confirm('Devam etmek istiyor musunuz? (Mevcut veriler korunacak)')) {
                    $this->info('❌ İşlem iptal edildi.');
                    return 0;
                }
            }

            // Force seçeneği ile mevcut verileri sil
            if ($force && $existingCount > 0) {
                $this->warn("🗑️  Force seçeneği aktif. {$existingCount} adet mevcut kayıt silinecek...");
                if (!$dryRun) {
                    User::truncate();
                    $this->info('✅ Mevcut veriler silindi.');
                }
            }

            // MySQL'den verileri çek (sadece status=1 olanlar)
            $this->info('📥 MySQL\'den aktif users verileri çekiliyor (status=1)...');
            $mysqlUsers = $this->migrationService->fetchMySQLUsers();

            if (empty($mysqlUsers)) {
                $this->warn('⚠️  MySQL\'de aktif users verisi bulunamadı.');
                return 0;
            }

            $this->info("📊 MySQL'de {" . count($mysqlUsers) . "} adet aktif user bulundu.");

            // MySQL'den user_branches2 verilerini çek (deleted_at null olanlar)
            $this->info('📥 MySQL\'den aktif user_branches2 verileri çekiliyor (deleted_at null)...');
            $mysqlUserBranches = $this->migrationService->fetchMySQLUserBranches();
            $this->info("📊 MySQL'de {" . count($mysqlUserBranches) . "} adet aktif user_branches2 kaydı bulundu.");

            //// Duplicate phone kontrolü
            //$this->info('🔍 Duplicate telefon numaraları kontrol ediliyor...');
            //$duplicatePhones = $this->migrationService->findDuplicatePhones();
            //if (!empty($duplicatePhones)) {
            //    $this->warn("⚠️  {" . count($duplicatePhones) . "} adet duplicate telefon numarası bulundu:");
            //    foreach (array_slice($duplicatePhones, 0, 5) as $duplicate) {
            //        $this->warn("  - {$duplicate['phone']} ({$duplicate['count']} kez)");
            //    }
            //    if (count($duplicatePhones) > 5) {
            //        $this->warn("  ... ve " . (count($duplicatePhones) - 5) . " tane daha");
            //    }
            //    $this->info("💡 Duplicate telefon numaraları otomatik olarak unique hale getirilecek (user_id eklenerek).");
            //}

            // Verileri işle
            $this->processUsers($mysqlUsers, $mysqlUserBranches, $dryRun, $chunkSize);

            // PostgreSQL sequence'larını güncelle
            if (!$dryRun) {
                $this->info('🔧 PostgreSQL sequence\'ları güncelleniyor...');
                $this->migrationService->updateUsersSequence();
                $this->migrationService->updateUserDealersSequence();
                $this->info('✅ Sequence\'lar güncellendi.');
            }

            // Doğrulama yap
            if ($validate && !$dryRun) {
                $this->info('🔍 Migration doğrulanıyor...');
                $issues = $this->migrationService->validateMigration();
                
                if (empty($issues)) {
                    $this->info('✅ Doğrulama başarılı. Tüm veriler doğru şekilde taşındı.');
                } else {
                    $this->warn('⚠️  Doğrulama sorunları:');
                    foreach ($issues as $issue) {
                        $this->warn("  - {$issue}");
                    }
                }
            }

            $this->info('🎉 Migration başarıyla tamamlandı!');
            
        } catch (Exception $e) {
            $this->error('❌ Hata: ' . $e->getMessage());
            $this->error('📍 Dosya: ' . $e->getFile() . ':' . $e->getLine());
            return 1;
        }

        return 0;
    }

    /**
     * Migration istatistiklerini göster
     */
    private function showMigrationStats()
    {
        $stats = $this->migrationService->getMigrationStats();
        
        $this->info('📊 Migration İstatistikleri:');
        $this->table(['Kaynak', 'Değer'], [
            ['MySQL Active Users (status=1)', $stats['mysql_users']],
            ['MySQL User Branches (deleted_at null)', $stats['mysql_user_branches']],
            ['PostgreSQL Users', $stats['postgresql_users']],
            ['PostgreSQL User-Dealers', $stats['postgresql_user_dealers']],
            ['Mevcut Roles', $stats['available_roles']],
            ['Mevcut Dealers', $stats['available_dealers']],
            ['Mevcut Regions', $stats['available_regions']],
            ['Mevcut Yedekler', $stats['available_backups']],
            ['Son Yedek', $stats['last_backup']['timestamp'] ?? 'Yok'],
        ]);
    }

    /**
     * Role kontrolü
     */
    private function checkRoles(): bool
    {
        $roleCount = Role::count();
        
        if ($roleCount === 0) {
            $this->error('❌ PostgreSQL\'de hiç role bulunamadı!');
            $this->error('💡 Önce roles migration\'ını çalıştırın: php artisan migrate:roles-from-mysql');
            return false;
        }

        $this->info("✅ {$roleCount} adet role bulundu.");
        return true;
    }

    /**
     * Bağlantıları test et
     */
    private function testConnections()
    {
        try {
            DB::connection('mysql')->getPdo();
            $this->info('✅ MySQL bağlantısı başarılı.');
        } catch (Exception $e) {
            throw new Exception('MySQL bağlantısı başarısız: ' . $e->getMessage());
        }

        try {
            DB::connection()->getPdo();
            $this->info('✅ PostgreSQL bağlantısı başarılı.');
        } catch (Exception $e) {
            throw new Exception('PostgreSQL bağlantısı başarısız: ' . $e->getMessage());
        }
    }

    /**
     * Users verilerini işle ve PostgreSQL'e kaydet
     */
    private function processUsers($mysqlUsers, $mysqlUserBranches, $dryRun, $chunkSize)
    {
        $chunks = array_chunk($mysqlUsers, $chunkSize);
        $totalProcessed = 0;
        $totalErrors = 0;

        $progressBar = $this->output->createProgressBar(count($mysqlUsers));
        $progressBar->start();

        foreach ($chunks as $chunk) {
            foreach ($chunk as $mysqlUser) {
                try {
                    $userData = $this->mapUserData($mysqlUser, $mysqlUserBranches);
                    
                    if ($dryRun) {
                        // User'ın kaç dealer'ı olacağını hesapla
                        $userDealerCount = 0;
                        foreach ($mysqlUserBranches as $userBranch) {
                            if ($userBranch->user_id == $mysqlUser->id) {
                                $userDealerCount++;
                            }
                        }

                        $this->line("\n🔍 Dry Run - İşlenecek veri:");
                        $this->table(['Alan', 'Değer'], [
                            ['ID (MySQL)', $mysqlUser->id ?? 'N/A'],
                            ['Name', $userData['name']],
                            ['Email', $userData['email']],
                            ['Phone', $userData['phone'] ?? 'N/A'],
                            ['Role ID (MySQL → PostgreSQL)', ($mysqlUser->user_role_group_id ?? 'N/A') . ' → ' . ($userData['role_id'] ?? 'N/A')],
                            ['Region ID', $userData['region_id'] ?? 'N/A'],
                            ['Zone ID (MySQL)', $mysqlUser->zone_id ?? 'N/A'],
                            ['Total Dealers', $userDealerCount],
                        ]);
                    } else {
                        // Aynı email'de user var mı kontrol et
                        $existingUser = User::where('email', $userData['email'])->first();

                        if ($existingUser) {
                            $this->warn("\n⚠️  '{$userData['email']}' email'li user zaten mevcut. Atlanıyor...");
                        } else {
                            $newUser = User::create($userData);

                            // User-Dealer ilişkilerini kaydet (MySQL user ID kullanarak)
                            $dealerRelationsCount = $this->migrationService->saveUserDealerRelationships(
                                $mysqlUser->id,
                                $mysqlUserBranches
                            );

                            $totalProcessed++;

                            if ($dealerRelationsCount > 0) {
                                $this->line("  ✅ {$dealerRelationsCount} dealer ilişkisi kaydedildi.");
                            }
                        }
                    }
                    
                    $progressBar->advance();
                    
                } catch (Exception $e) {
                    $totalErrors++;
                    $this->error("\n❌ Hata (ID: {$mysqlUser->id}): " . $e->getMessage());
                    $progressBar->advance();
                }
            }
        }

        $progressBar->finish();
        $this->newLine(2);

        if ($dryRun) {
            $this->info("🔍 Dry Run tamamlandı. {" . count($mysqlUsers) . "} kayıt işlenebilir durumda.");
        } else {
            $this->info("✅ {$totalProcessed} kayıt başarıyla işlendi.");
            if ($totalErrors > 0) {
                $this->warn("⚠️  {$totalErrors} kayıtta hata oluştu.");
            }
        }
    }

    /**
     * MySQL user verisini PostgreSQL user formatına dönüştür
     */
    private function mapUserData($mysqlUser, $mysqlUserBranches)
    {
        // MySQL'deki user_role_group_id'yi PostgreSQL role_id'ye çevir
        $roleId = null;
        if (isset($mysqlUser->user_role_group_id) && $mysqlUser->user_role_group_id) {
            $roleId = $this->migrationService->getRoleIdByMySQLRoleId($mysqlUser->user_role_group_id);
        }
        // Eğer role bulunamazsa ilk role'ı al
        if (!$roleId) {
            $roleId = null;
        }

        // MySQL zone_id'yi PostgreSQL region_id'ye çevir
        $regionId = null;
        if (isset($mysqlUser->zone_id) && $mysqlUser->zone_id) {
            $regionId = $this->migrationService->getRegionIdByZoneId($mysqlUser->zone_id);
        }
        // Eğer region bulunamazsa ilk region'ı al
        if (!$regionId) {
            $regionId = null;
        }

        return [
            'id' => $mysqlUser->id,
            'name' => ($mysqlUser->name && $mysqlUser->surname)
                ? $mysqlUser->name . ' ' . $mysqlUser->surname
                : 'Bilinmeyen Kullanıcı',
            'email' => $mysqlUser->email ?? 'unknown' . rand(1000, 99999) . '@example.com',
            'phone' => $mysqlUser->telephone ?? null,
            'password' => $mysqlUser->password,
            'role_id' => $roleId,
            'region_id' => $regionId,
            'email_verified_at' => $mysqlUser->email_verified_at ?? null,
            'remember_token' => $mysqlUser->remember_token ?? null,
            'created_at' => $mysqlUser->created_at ?? now(),
            'updated_at' => $mysqlUser->updated_at ?? now(),
        ];

    }
}
