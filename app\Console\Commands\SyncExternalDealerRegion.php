<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\ExternalDealerRegion;
use App\Models\DealerRegion;
use App\Models\Dealer;
class SyncExternalDealerRegion extends Command
{


    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:external-dealer-region';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'MySQL veritabanından region verilerini PostgreSQL\'e aktarır';

    
    /**
     * Execute the console command.
     */
    public function handle()
    {
        $externalDealerRegions = ExternalDealerRegion::all();

        
        foreach ($externalDealerRegions as $external) {
            // Dealer var mı kontrol et
            if (!Dealer::where('id', $external->branch_id)->exists()) {
                $this->warn("Dealer bulunamadı: ID {$external->branch_id} — atlanıyor.");
                continue;
            }

            DealerRegion::updateOrCreate(
                [
                    'dealer_id' => $external->branch_id,
                    'region_id' => $external->zone_id,
                ],
                []
            );

                $this->info("Veriler başarıyla senkronize edildi.");
            }
       }
}