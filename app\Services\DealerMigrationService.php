<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use App\Models\Dealer;
use App\Models\DealerRegion;
use App\Models\Region;
use Exception;
use Carbon\Carbon;

class DealerMigrationService
{
    private $backupPath = 'migrations/dealers';

    /**
     * MySQL'den branches verilerini çek
     */
    public function fetchMySQLBranches(): array
    {
        try {
            return DB::connection('mysql')
                ->table('branches')
                ->orderBy('id')
                ->get()
                ->toArray();
        } catch (Exception $e) {
            throw new Exception('MySQL\'den branches verisi çekme hatası: ' . $e->getMessage());
        }
    }

    /**
     * Mevcut PostgreSQL dealers verilerini yedekle
     */
    public function backupCurrentDealers(): string
    {
        $timestamp = Carbon::now()->format('Y-m-d_H-i-s');
        $backupFile = "{$this->backupPath}/dealers_backup_{$timestamp}.json";

        $currentDealers = Dealer::with('region', 'customers')->get()->toArray();
        
        $backupData = [
            'timestamp' => $timestamp,
            'total_dealers' => count($currentDealers),
            'dealers' => $currentDealers,
            'metadata' => [
                'php_version' => PHP_VERSION,
                'laravel_version' => app()->version(),
                'database_connection' => config('database.default'),
                'created_by' => 'DealerMigrationService',
            ]
        ];

        Storage::put($backupFile, json_encode($backupData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

        return $backupFile;
    }

    /**
     * Yedekten geri yükle
     */
    public function restoreFromBackup(string $backupFile): bool
    {
        if (!Storage::exists($backupFile)) {
            throw new Exception("Yedek dosyası bulunamadı: {$backupFile}");
        }

        $backupData = json_decode(Storage::get($backupFile), true);
        
        if (!$backupData || !isset($backupData['dealers'])) {
            throw new Exception('Geçersiz yedek dosyası formatı');
        }

        DB::beginTransaction();
        try {
            // Mevcut verileri sil
            Dealer::truncate();

            // Yedekten verileri geri yükle
            foreach ($backupData['dealers'] as $dealerData) {
                Dealer::create([
                    'id' => $dealerData['id'] ?? null,
                    'region_id' => $dealerData['region_id'],
                    'name' => $dealerData['name'],
                    'contact_person' => $dealerData['contact_person'],
                    'phone' => $dealerData['phone'],
                    'email' => $dealerData['email'],
                    'address' => $dealerData['address'],
                    'city' => $dealerData['city'],
                    'district' => $dealerData['district'],
                    'status' => $dealerData['status'],
                    'created_at' => $dealerData['created_at'],
                    'updated_at' => $dealerData['updated_at'],
                ]);
            }

            // Sequence'ı güncelle
            $this->updateDealersSequence();

            DB::commit();
            return true;
        } catch (Exception $e) {
            DB::rollback();
            throw new Exception('Geri yükleme hatası: ' . $e->getMessage());
        }
    }

    /**
     * Mevcut yedek dosyalarını listele
     */
    public function listBackups(): array
    {
        $files = Storage::files($this->backupPath);
        $backups = [];

        foreach ($files as $file) {
            if (str_ends_with($file, '.json') && str_contains($file, 'dealers_backup_')) {
                $content = json_decode(Storage::get($file), true);
                $backups[] = [
                    'file' => $file,
                    'timestamp' => $content['timestamp'] ?? 'Unknown',
                    'total_dealers' => $content['total_dealers'] ?? 0,
                    'size' => Storage::size($file),
                    'created_at' => Storage::lastModified($file),
                ];
            }
        }

        // Tarihe göre sırala (en yeni önce)
        usort($backups, function($a, $b) {
            return $b['created_at'] <=> $a['created_at'];
        });

        return $backups;
    }

    /**
     * Eski yedek dosyalarını temizle
     */
    public function cleanOldBackups(int $keepCount = 5): int
    {
        $backups = $this->listBackups();
        $deletedCount = 0;

        if (count($backups) > $keepCount) {
            $toDelete = array_slice($backups, $keepCount);
            
            foreach ($toDelete as $backup) {
                Storage::delete($backup['file']);
                $deletedCount++;
            }
        }

        return $deletedCount;
    }

    /**
     * Migration istatistiklerini al
     */
    public function getMigrationStats(): array
    {
        try {
            $mysqlCount = DB::connection('mysql')->table('branches')->count();
        } catch (Exception $e) {
            $mysqlCount = 'Bağlantı Hatası';
        }

        $postgresqlCount = Dealer::count();
        $backupsCount = count($this->listBackups());
        $regionsCount = Region::count();

        return [
            'mysql_branches' => $mysqlCount,
            'postgresql_dealers' => $postgresqlCount,
            'available_regions' => $regionsCount,
            'available_backups' => $backupsCount,
            'last_backup' => $this->getLastBackupInfo(),
        ];
    }

    /**
     * Son yedek bilgisini al
     */
    private function getLastBackupInfo(): ?array
    {
        $backups = $this->listBackups();
        return $backups[0] ?? null;
    }

    /**
     * Veri doğrulama
     */
    public function validateMigration(): array
    {
        $issues = [];

        try {
            $mysqlBranches = $this->fetchMySQLBranches();
            $postgresqlDealers = Dealer::all();

            // Sayı kontrolü
            if (count($mysqlBranches) !== $postgresqlDealers->count()) {
                $issues[] = "Kayıt sayısı uyumsuzluğu: MySQL Branches({" . count($mysqlBranches) . "}) vs PostgreSQL Dealers({$postgresqlDealers->count()})";
            }

            // İsim kontrolü
            $mysqlNames = array_column($mysqlBranches, 'name');
            $postgresqlNames = $postgresqlDealers->pluck('name')->toArray();
            
            $missingInPostgres = array_diff($mysqlNames, $postgresqlNames);
            $extraInPostgres = array_diff($postgresqlNames, $mysqlNames);

            if (!empty($missingInPostgres)) {
                $issues[] = "PostgreSQL'de eksik: " . implode(', ', $missingInPostgres);
            }

            if (!empty($extraInPostgres)) {
                $issues[] = "PostgreSQL'de fazla: " . implode(', ', $extraInPostgres);
            }

            // Region kontrolü
            $dealersWithoutRegion = $postgresqlDealers->whereNull('region_id')->count();
            if ($dealersWithoutRegion > 0) {
                $issues[] = "Region'u olmayan dealer sayısı: {$dealersWithoutRegion}";
            }

        } catch (Exception $e) {
            $issues[] = "Doğrulama hatası: " . $e->getMessage();
        }

        return $issues;
    }

    /**
     * İlk region ID'sini al
     */
    public function getFirstRegionId(): ?int
    {
        $firstRegion = Region::first();
        return $firstRegion ? $firstRegion->id : null;
    }

    /**
     * Zone ID'ye göre PostgreSQL region_id'sini al
     * MySQL zones.id → PostgreSQL regions.id eşleştirmesi
     */
    public function getRegionIdFromZoneId(?int $zoneId): ?int
    {
        if (!$zoneId) {
            return $this->getFirstRegionId();
        }

        // MySQL zone_id'yi PostgreSQL regions tablosunda ara
        // (regions migration'ında zones.id → regions.id olarak ID'ler korunuyor)
        $region = Region::find($zoneId);

        if ($region) {
            return $region->id;
        }

        // Eğer zone_id'ye karşılık gelen region yoksa, ilk region'u kullan
        return $this->getFirstRegionId();
    }

    /**
     * PostgreSQL dealers tablosunun sequence'ını güncelle
     */
    public function updateDealersSequence(): void
    {
        try {
            // En yüksek ID'yi al
            $maxId = Dealer::max('id') ?? 0;

            // Sequence'ı güncelle
            DB::statement("SELECT setval('dealers_id_seq', {$maxId}, true)");

        } catch (Exception $e) {
            throw new Exception('Dealers sequence güncelleme hatası: ' . $e->getMessage());
        }
    }

    /**
     * Dealer-Region ilişkilerini kaydet
     */
    public function saveDealerRegionRelationships($dealerId, $mysqlBranchId): int
    {
        $savedCount = 0;

        try {
            // Bu branch_id için tüm zone_id'leri al
            $zoneBranches = DB::connection('mysql')
                ->table('zone_branches')
                ->where('branch_id', $mysqlBranchId)
                ->whereNull('deleted_at')
                ->get();

            foreach ($zoneBranches as $zoneBranch) {
                $regionId = $this->getRegionIdFromZoneId($zoneBranch->zone_id);

                if ($regionId) {
                    // Aynı ilişki zaten var mı kontrol et
                    $existingRelation = DealerRegion::where('dealer_id', $dealerId)
                        ->where('region_id', $regionId)
                        ->first();

                    if (!$existingRelation) {
                        DealerRegion::create([
                            'dealer_id' => $dealerId,
                            'region_id' => $regionId,
                        ]);
                        $savedCount++;
                    }
                }
            }

            return $savedCount;
        } catch (Exception $e) {
            throw new Exception('Dealer-Region ilişki kaydetme hatası: ' . $e->getMessage());
        }
    }

    /**
     * MySQL'de zone_id'ye göre kayıt sayısını kontrol et
     */
    public function checkMySQLZoneBranchesCount($zoneId): array
    {
        try {
            // zone_branches tablosunda zone_id=2 için kayıt sayısı
            $zoneBranchesCount = DB::connection('mysql')
                ->table('zone_branches')
                ->where('zone_id', $zoneId)
                ->count();

            // branches tablosunda bu zone_id'ye sahip kayıtlar
            $branchesWithZone = DB::connection('mysql')
                ->table('branches')
                ->join('zone_branches', 'branches.id', '=', 'zone_branches.branch_id')
                ->where('zone_branches.zone_id', $zoneId)
                ->count();

            // Tüm branches kayıtları (zone_branches ile JOIN)
            $allBranchesWithZone = DB::connection('mysql')
                ->table('branches')
                ->leftJoin('zone_branches', 'branches.id', '=', 'zone_branches.branch_id')
                ->where('zone_branches.zone_id', $zoneId)
                ->get();

            return [
                'zone_branches_count' => $zoneBranchesCount,
                'branches_with_zone_count' => $branchesWithZone,
                'detailed_records' => $allBranchesWithZone->toArray()
            ];
        } catch (Exception $e) {
            throw new Exception('MySQL zone_branches kontrol hatası: ' . $e->getMessage());
        }
    }
}
