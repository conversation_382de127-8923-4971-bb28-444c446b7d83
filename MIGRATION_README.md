# MySQL'den PostgreSQL'e Migration Sistemi

Bu sistem, MySQL veritabanından PostgreSQL'e regions, dealers ve users verilerini güvenli ve profesyonel bir şekilde taşımak için geliştirilmiştir.

## 🚀 Özellikler

- **Güvenli Migration**: Yedekleme ve geri yükleme sistemi
- **Dry Run**: Gerçek veri değişikliği yapmadan önizleme
- **Chunk Processing**: Büyük veri setleri için parça parça işleme
- **Validation**: Migration sonrası veri doğrulama
- **Backup Management**: Otomatik yedekleme ve temizleme
- **Interactive Restore**: Kullanıcı dostu geri yükleme
- **Simple Region Assignment**: Dealers için basit region ataması

## 📋 Gereksinimler

- Laravel 11+
- MySQL ve PostgreSQL bağlantıları
- PHP 8.1+
- Yet<PERSON><PERSON> disk alanı (<PERSON><PERSON><PERSON> için)

## ⚙️ Kurulum

1. **Service Provider otomatik olarak yüklenir** (Laravel 11 auto-discovery)

2. **MySQL bağlantısını yapılandırın** (.env dosyasında):
```env
DB_UMRAM_HOST=mysql_host
DB_UMRAM_PORT=3306
DB_UMRAM_DATABASE=mysql_database
DB_UMRAM_USERNAME=mysql_user
DB_UMRAM_PASSWORD=mysql_password
```

3. **Storage klasörünü oluşturun**:
```bash
mkdir -p storage/app/migrations/regions
```

## 🔧 Kullanım

### 1. Migration İstatistiklerini Görüntüleme

```bash
php artisan migrate:regions-from-mysql --dry-run
```

### 2. Yedek Alma

```bash
# Manuel yedek alma
php artisan backup:regions

# Mevcut yedekleri listeleme
php artisan backup:regions --list

# Eski yedekleri temizleme (son 3 yedek korunur)
php artisan backup:regions --clean=3
```

### 3. Migration Sırası

Migration'ları aşağıdaki sırayla çalıştırın:

1. **Roles Migration** (önce - users için gerekli)
2. **Regions Migration** (dealers ve users için gerekli)
3. **Dealers Migration** (regions'dan sonra, users için gerekli)
4. **Users Migration** (en son - roles, regions ve dealers'a bağımlı)

### 4. Migration Yapma

#### Roles Migration:
```bash
# Önizleme (veri değişikliği yapmaz)
php artisan migrate:roles-from-mysql --dry-run

# Yedek alarak migration
php artisan migrate:roles-from-mysql --backup --validate

# Mevcut verileri silerek migration
php artisan migrate:roles-from-mysql --force --backup --validate
```

**Roles Migration Özellikleri:**
- MySQL user_role_groups → PostgreSQL roles dönüşümü
- MySQL ID'leri korunur (PostgreSQL'de aynı ID'ler kullanılır)
- PostgreSQL sequence otomatik güncellenir

#### Regions Migration:
```bash
# Önizleme (veri değişikliği yapmaz)
php artisan migrate:regions-from-mysql --dry-run

# Yedek alarak migration
php artisan migrate:regions-from-mysql --backup --validate

# Mevcut verileri silerek migration
php artisan migrate:regions-from-mysql --force --backup --validate
```

#### Dealers Migration:
```bash
# Önizleme (veri değişikliği yapmaz)
php artisan migrate:dealers-from-mysql --dry-run

# Yedek alarak migration (ilk region'a atanır)
php artisan migrate:dealers-from-mysql --backup --validate

# Mevcut verileri silerek migration
php artisan migrate:dealers-from-mysql --force --backup --validate
```

#### Users Migration:
```bash
# Önizleme (veri değişikliği yapmaz)
php artisan migrate:users-from-mysql --dry-run

# Yedek alarak migration
php artisan migrate:users-from-mysql --backup --validate

# Mevcut verileri silerek migration
php artisan migrate:users-from-mysql --force --backup --validate
```

**Users Migration Özellikleri:**
- Sadece aktif kullanıcılar alınır (MySQL status=1)
- Sadece aktif user-branch ilişkileri alınır (MySQL user_branches2.deleted_at null)
- MySQL ID'leri korunur (PostgreSQL'de aynı ID'ler kullanılır)
- MySQL `role_id` → PostgreSQL `role_id` (ID'ler korunarak eşleştirilir)
- MySQL `zone_id` → PostgreSQL `region_id` dönüşümü
- MySQL `user_branches2` → PostgreSQL `user_dealers` many-to-many ilişki
- Bir kullanıcının tüm branch'leri user_dealers tablosuna kaydedilir
- Users tablosunda dealer_id alanı kullanılmaz (sadece many-to-many)
- Role veya region bulunamazsa varsayılan değerler atanır

### 5. Geri Yükleme

#### Regions Geri Yükleme:
```bash
# İnteraktif geri yükleme
php artisan restore:regions

# En son yedekten geri yükleme
php artisan restore:regions --latest

# Belirli yedekten geri yükleme
php artisan restore:regions regions_backup_2025-01-30_14-30-15.json
```

#### Dealers Geri Yükleme:
```bash
# İnteraktif geri yükleme
php artisan restore:dealers

# En son yedekten geri yükleme
php artisan restore:dealers --latest

# Belirli yedekten geri yükleme
php artisan restore:dealers dealers_backup_2025-01-30_14-30-15.json
```

#### Users Geri Yükleme:
```bash
# İnteraktif geri yükleme (user-dealer ilişkileri dahil)
php artisan restore:users

# En son yedekten geri yükleme
php artisan restore:users --latest

# Belirli yedekten geri yükleme
php artisan restore:users users_backup_2025-01-30_14-30-15.json
```

#### Yedek Yönetimi:
```bash
# Regions yedekleri
php artisan backup:regions --list
php artisan backup:regions --clean=3

# Dealers yedekleri
php artisan backup:dealers --list
php artisan backup:dealers --clean=3

# Users yedekleri
php artisan backup:users --list
php artisan backup:users --clean=3
```

## 📊 Command Parametreleri

### migrate:regions-from-mysql

| Parametre | Açıklama |
|-----------|----------|
| `--dry-run` | Sadece önizleme yap, veri kaydetme |
| `--force` | Mevcut verileri sil ve yeniden yükle |
| `--chunk=100` | Kaç kayıt gruplarında işle |
| `--backup` | Migration öncesi yedek al |
| `--validate` | Migration sonrası doğrulama yap |

### backup:regions

| Parametre | Açıklama |
|-----------|----------|
| `--list` | Mevcut yedekleri listele |
| `--clean=5` | Eski yedekleri temizle (kaç tane tutulacak) |

### restore:regions

| Parametre | Açıklama |
|-----------|----------|
| `backup` | Geri yüklenecek yedek dosyası (opsiyonel) |
| `--latest` | En son yedekten geri yükle |
| `--list` | Mevcut yedekleri listele |

## 🔒 Güvenlik Özellikleri

1. **Otomatik Yedekleme**: Her migration öncesi mevcut veriler yedeklenir
2. **Transaction Kullanımı**: Hata durumunda rollback
3. **Validation**: Migration sonrası veri bütünlüğü kontrolü
4. **Confirmation**: Kritik işlemler için kullanıcı onayı
5. **Error Handling**: Detaylı hata raporlama

## 📁 Dosya Yapısı

```
app/
├── Console/Commands/
│   ├── MigrateRegionsFromMySQL.php
│   ├── BackupRegions.php
│   └── RestoreRegions.php
├── Services/
│   └── RegionMigrationService.php
└── Providers/
    └── MigrationServiceProvider.php

storage/app/migrations/regions/
├── regions_backup_2025-01-30_14-30-15.json
├── regions_backup_2025-01-30_15-45-22.json
└── ...
```

## 🚨 Önemli Notlar

1. **Yedek Alın**: Migration öncesi mutlaka yedek alın
2. **Test Edin**: Önce test ortamında deneyin
3. **Disk Alanı**: Yedekler için yeterli disk alanı olduğundan emin olun
4. **Bağlantılar**: MySQL ve PostgreSQL bağlantılarını test edin
5. **Downtime**: Migration sırasında sistem kullanımını durdurun

## 🔍 Troubleshooting

### Bağlantı Hataları
```bash
# MySQL bağlantısını test et
php artisan tinker
DB::connection('mysql')->getPdo();
```

### Yedek Dosyası Bulunamadı
```bash
# Yedek klasörünü kontrol et
ls -la storage/app/migrations/regions/
```

### Migration Doğrulama Hataları
```bash
# Manuel doğrulama
php artisan migrate:regions-from-mysql --dry-run --validate
```

## 📞 Destek

Migration sırasında sorun yaşarsanız:

1. Log dosyalarını kontrol edin
2. `--dry-run` ile önizleme yapın
3. Yedekten geri yükleyin
4. Geliştirici ekibi ile iletişime geçin

## 🎯 En İyi Uygulamalar

1. **Migration öncesi**: `--dry-run` ile test edin
2. **Yedek alın**: `--backup` parametresini kullanın
3. **Doğrulayın**: `--validate` ile kontrol edin
4. **Temizleyin**: Eski yedekleri düzenli temizleyin
5. **Monitör edin**: Migration sürecini takip edin
