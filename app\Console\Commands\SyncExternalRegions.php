<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\ExternalRegions;
use App\Models\Region;

class SyncExternalRegions extends Command
{


    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:external-regions';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'MySQL veritabanından region verilerini PostgreSQL\'e aktarır';

    
    /**
     * Execute the console command.
     */
    public function handle()
    {
        $externalRegions = ExternalRegions::all();

        foreach ($externalRegions as $external) {
            Region::updateOrCreate(
                ['id' => $external->id], // eşleşme kriteri
                [
                    'name' => $external->name,
                    'description' => null,
                    'status' => in_array($external->status, [1, '1', true], true),
                ]
            );
        }

        $this->info("Veriler başarıyla senkronize edildi.");
    }
}