<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\ExternalDealers;
use App\Models\Dealer;

class SyncExternalDealers extends Command
{


    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:external-dealers';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'MySQL veritabanından dealer verilerini PostgreSQL\'e aktarır';

    
    /**
     * Execute the console command.
     */
    public function handle()
    {
        $externalDealers = ExternalDealers::all();

        foreach ($externalDealers as $external) {
            Dealer::updateOrCreate(
                ['id' => $external->id], // eşleşme kriteri
                [
                    'name' => $external->kisa_ad,
                    'contact_person' => $external->yetkili_ad_soyad,
                    'phone' => $external->gsm,
                    'email' => $external->email,
                    'address' => $external->address,
                    'status' => in_array($external->status, [1, '1', true], true),
                ]
            );
        }

        $this->info("Veriler başarıyla senkronize edildi.");
    }
}